/**
 * Debug utilities for logging with content length limits
 */

import { ConversationMessage } from "../shared/types"
import { MCPToolCall, MCPToolResult } from "./mcp-service"

export interface LogContentOptions {
  maxLength?: number
  showLength?: boolean
  prefix?: string
  suffix?: string
}

/**
 * Truncates content for logging with configurable options
 */
export function truncateForLog(
  content: string,
  options: LogContentOptions = {}
): string {
  const {
    maxLength = 200,
    showLength = true,
    prefix = "",
    suffix = "..."
  } = options

  if (!content) return content

  const lengthInfo = showLength ? ` (${content.length} chars)` : ""

  if (content.length <= maxLength) {
    return `${prefix}${content}${lengthInfo}`
  }

  const truncated = content.substring(0, maxLength)
  return `${prefix}${truncated}${suffix}${lengthInfo}`
}

/**
 * Logs conversation messages with content truncation
 */
export function logConversationMessages(
  messages: Array<{ role: string; content: string }>,
  logPrefix: string = "[CONVERSATION-DEBUG]",
  maxContentLength: number = 150
): void {
  console.log(`${logPrefix} 📝 Messages (${messages.length} total):`)
  messages.forEach((msg, i) => {
    const truncatedContent = truncateForLog(msg.content, {
      maxLength: maxContentLength,
      showLength: true
    })
    console.log(`${logPrefix}   ${i + 1}. ${msg.role}: ${truncatedContent}`)
  })
}

/**
 * Logs system prompt with truncation
 */
export function logSystemPrompt(
  systemPrompt: string,
  logPrefix: string = "[SYSTEM-PROMPT-DEBUG]",
  maxLength: number = 300
): void {
  const truncated = truncateForLog(systemPrompt, {
    maxLength,
    showLength: true,
    prefix: "📋 System prompt: "
  })
  console.log(`${logPrefix} ${truncated}`)
}

/**
 * Logs LLM response with truncation
 */
export function logLLMResponse(
  response: string,
  logPrefix: string = "[LLM-RESPONSE-DEBUG]",
  maxLength: number = 200
): void {
  const truncated = truncateForLog(response, {
    maxLength,
    showLength: true,
    prefix: "🤖 Response: "
  })
  console.log(`${logPrefix} ${truncated}`)
}

/**
 * Logs conversation state changes with focused information
 */
export function logConversationState(
  state: {
    conversationId?: string | null
    messageCount?: number
    lastMessageRole?: string
    isActive?: boolean
    showContinueButton?: boolean
    isWaitingForResponse?: boolean
  },
  logPrefix: string = "[CONVERSATION-STATE]"
): void {
  const {
    conversationId,
    messageCount = 0,
    lastMessageRole,
    isActive = false,
    showContinueButton = false,
    isWaitingForResponse = false
  } = state

  console.log(`${logPrefix} 🔄 State change:`, {
    id: conversationId ? `${conversationId.substring(0, 8)}...` : null,
    messages: messageCount,
    lastRole: lastMessageRole,
    active: isActive,
    canContinue: showContinueButton,
    waiting: isWaitingForResponse
  })
}

/**
 * Logs conversation loading/saving operations
 */
export function logConversationOperation(
  operation: 'load' | 'save' | 'create' | 'delete',
  conversationId: string,
  details?: {
    messageCount?: number
    title?: string
    error?: string
  },
  logPrefix: string = "[CONVERSATION-OP]"
): void {
  const shortId = conversationId.substring(0, 8) + "..."
  const emoji = {
    load: "📂",
    save: "💾",
    create: "🆕",
    delete: "🗑️"
  }[operation]

  if (details?.error) {
    console.error(`${logPrefix} ${emoji} ${operation.toUpperCase()} failed for ${shortId}:`, details.error)
  } else {
    const info = details ? ` (${details.messageCount || 0} msgs${details.title ? `, "${details.title.substring(0, 30)}..."` : ""})` : ""
    console.log(`${logPrefix} ${emoji} ${operation.toUpperCase()} ${shortId}${info}`)
  }
}

/**
 * Logs agent mode iterations with focused information
 */
export function logAgentIteration(
  iteration: number,
  maxIterations: number,
  details: {
    hasToolCalls?: boolean
    toolCallCount?: number
    isComplete?: boolean
    responseLength?: number
  },
  logPrefix: string = "[AGENT-ITERATION]"
): void {
  const {
    hasToolCalls = false,
    toolCallCount = 0,
    isComplete = false,
    responseLength = 0
  } = details

  console.log(`${logPrefix} 🔄 Iteration ${iteration}/${maxIterations}:`, {
    tools: hasToolCalls ? toolCallCount : 0,
    complete: isComplete,
    responseLen: responseLength
  })
}

/**
 * Converts conversation messages to agent mode history format
 */
export function convertConversationToAgentHistory(
  messages: ConversationMessage[]
): Array<{
  role: "user" | "assistant" | "tool"
  content: string
  toolCalls?: MCPToolCall[]
  toolResults?: MCPToolResult[]
}> {
  return messages.map(msg => ({
    role: msg.role,
    content: msg.content,
    toolCalls: msg.toolCalls as MCPToolCall[] | undefined,
    toolResults: msg.toolResults as MCPToolResult[] | undefined
  }))
}
