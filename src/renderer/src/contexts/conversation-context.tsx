import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from "react"
import { Conversation, ConversationMessage } from "@shared/types"
import {
  useCreateConversationMutation,
  useAddMessageToConversationMutation,
  useSaveConversationMutation,
  useConversationQuery
} from "@renderer/lib/query-client"
import { logConversationState, logConversationAction } from "@renderer/lib/debug-utils"

interface ConversationContextType {
  // Current conversation state
  currentConversation: Conversation | null
  isConversationActive: boolean

  // Conversation management
  startNewConversation: (firstMessage: string, role?: "user" | "assistant") => Promise<Conversation | null>
  continueConversation: (conversationId: string) => void
  addMessage: (content: string, role: "user" | "assistant" | "tool", toolCalls?: any[], toolResults?: any[]) => Promise<void>
  endConversation: () => void

  // UI state
  showContinueButton: boolean
  setShowContinueButton: (show: boolean) => void
  isWaitingForResponse: boolean
  setIsWaitingForResponse: (waiting: boolean) => void
}

const ConversationContext = createContext<ConversationContextType | undefined>(undefined)

interface ConversationProviderProps {
  children: ReactNode
}

export function ConversationProvider({ children }: ConversationProviderProps) {
  // Initialize conversation ID from localStorage if available
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(() => {
    try {
      return localStorage.getItem('currentConversationId')
    } catch {
      return null
    }
  })
  const [showContinueButton, setShowContinueButton] = useState(false)
  const [isWaitingForResponse, setIsWaitingForResponse] = useState(false)

  // Persist current conversation ID to localStorage
  useEffect(() => {
    try {
      if (currentConversationId) {
        localStorage.setItem('currentConversationId', currentConversationId)
      } else {
        localStorage.removeItem('currentConversationId')
      }
    } catch (error) {
      console.warn('Failed to persist conversation ID to localStorage:', error)
    }
  }, [currentConversationId])

  // Queries and mutations
  const conversationQuery = useConversationQuery(currentConversationId)
  const createConversationMutation = useCreateConversationMutation()
  const addMessageMutation = useAddMessageToConversationMutation()
  const saveConversationMutation = useSaveConversationMutation()

  const currentConversation = conversationQuery.data || null
  const isConversationActive = !!currentConversation

  // Effect to restore continue button state when conversation is loaded
  useEffect(() => {
    if (currentConversation && currentConversation.messages.length > 0) {
      const lastMessage = currentConversation.messages[currentConversation.messages.length - 1]
      // Show continue button if the last message is from assistant and we're not waiting for response
      // This provides an explicit way to continue conversations
      if (lastMessage.role === "assistant" && !isWaitingForResponse) {
        setShowContinueButton(true)
      }
    } else {
      // No conversation or no messages, hide continue button
      setShowContinueButton(false)
    }
  }, [currentConversation, isWaitingForResponse])

  const startNewConversation = useCallback(async (
    firstMessage: string,
    role: "user" | "assistant" = "user"
  ): Promise<Conversation | null> => {
    try {
      const conversation = await createConversationMutation.mutateAsync({ firstMessage, role })
      setCurrentConversationId(conversation.id)
      setShowContinueButton(false)
      setIsWaitingForResponse(false)

      logConversationAction('start', {
        conversationId: conversation.id,
        messageRole: role,
        messageLength: firstMessage.length
      })

      return conversation
    } catch (error) {
      logConversationAction('start', {
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }, [createConversationMutation])

  const continueConversation = useCallback((conversationId: string) => {
    setCurrentConversationId(conversationId)
    setShowContinueButton(false)
    setIsWaitingForResponse(false)

    logConversationAction('continue', { conversationId })
  }, [])

  const addMessage = useCallback(async (
    content: string,
    role: "user" | "assistant" | "tool",
    toolCalls?: Array<{ name: string; arguments: any }>,
    toolResults?: Array<{ success: boolean; content: string; error?: string }>
  ) => {
    if (!currentConversationId) {
      logConversationAction('add-message', {
        error: "No active conversation"
      })
      return
    }

    try {
      await addMessageMutation.mutateAsync({
        conversationId: currentConversationId,
        content,
        role,
        toolCalls,
        toolResults
      })

      logConversationAction('add-message', {
        conversationId: currentConversationId,
        messageRole: role,
        messageLength: content.length
      })

      // Show continue button after assistant response
      if (role === "assistant") {
        setShowContinueButton(true)
        setIsWaitingForResponse(false)
      }
    } catch (error) {
      logConversationAction('add-message', {
        conversationId: currentConversationId,
        error: error instanceof Error ? error.message : String(error)
      })
      setIsWaitingForResponse(false)
    }
  }, [currentConversationId, addMessageMutation])

  const endConversation = useCallback(() => {
    const prevId = currentConversationId
    setCurrentConversationId(null)
    setShowContinueButton(false)
    setIsWaitingForResponse(false)

    logConversationAction('end', { conversationId: prevId })
  }, [currentConversationId])

  const contextValue: ConversationContextType = {
    currentConversation,
    isConversationActive,
    startNewConversation,
    continueConversation,
    addMessage,
    endConversation,
    showContinueButton,
    setShowContinueButton,
    isWaitingForResponse,
    setIsWaitingForResponse
  }

  return (
    <ConversationContext.Provider value={contextValue}>
      {children}
    </ConversationContext.Provider>
  )
}

export function useConversation() {
  const context = useContext(ConversationContext)
  if (context === undefined) {
    throw new Error("useConversation must be used within a ConversationProvider")
  }
  return context
}

// Hook for managing conversation state in components
export function useConversationState() {
  const {
    currentConversation,
    isConversationActive,
    showContinueButton,
    isWaitingForResponse
  } = useConversation()

  return {
    currentConversation,
    isConversationActive,
    showContinueButton,
    isWaitingForResponse,
    hasMessages: currentConversation?.messages.length ?? 0 > 0,
    lastMessage: currentConversation?.messages[currentConversation.messages.length - 1] || null
  }
}

// Hook for conversation actions
export function useConversationActions() {
  const {
    startNewConversation,
    continueConversation,
    addMessage,
    endConversation,
    setShowContinueButton,
    setIsWaitingForResponse
  } = useConversation()

  return {
    startNewConversation,
    continueConversation,
    addMessage,
    endConversation,
    setShowContinueButton,
    setIsWaitingForResponse
  }
}
