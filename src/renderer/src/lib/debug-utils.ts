/**
 * Debug utilities for renderer process logging with content length limits
 */

export interface LogContentOptions {
  maxLength?: number
  showLength?: boolean
  prefix?: string
  suffix?: string
}

/**
 * Truncates content for logging with configurable options
 */
export function truncateForLog(
  content: string, 
  options: LogContentOptions = {}
): string {
  const {
    maxLength = 200,
    showLength = true,
    prefix = "",
    suffix = "..."
  } = options

  if (!content) return content

  const lengthInfo = showLength ? ` (${content.length} chars)` : ""
  
  if (content.length <= maxLength) {
    return `${prefix}${content}${lengthInfo}`
  }

  const truncated = content.substring(0, maxLength)
  return `${prefix}${truncated}${suffix}${lengthInfo}`
}

/**
 * Logs conversation state changes with focused information
 */
export function logConversationState(
  state: {
    conversationId?: string | null
    messageCount?: number
    lastMessageRole?: string
    isActive?: boolean
    showContinueButton?: boolean
    isWaitingForResponse?: boolean
  },
  logPrefix: string = "[CONVERSATION-STATE]"
): void {
  const {
    conversationId,
    messageCount = 0,
    lastMessageRole,
    isActive = false,
    showContinueButton = false,
    isWaitingForResponse = false
  } = state

  console.log(`${logPrefix} 🔄 State change:`, {
    id: conversationId ? `${conversationId.substring(0, 8)}...` : null,
    messages: messageCount,
    lastRole: lastMessageRole,
    active: isActive,
    canContinue: showContinueButton,
    waiting: isWaitingForResponse
  })
}

/**
 * Logs conversation actions with focused information
 */
export function logConversationAction(
  action: 'start' | 'continue' | 'add-message' | 'end',
  details: {
    conversationId?: string | null
    messageRole?: string
    messageLength?: number
    error?: string
  } = {},
  logPrefix: string = "[CONVERSATION-ACTION]"
): void {
  const { conversationId, messageRole, messageLength, error } = details
  const shortId = conversationId ? `${conversationId.substring(0, 8)}...` : 'none'
  
  const emoji = {
    start: "🆕",
    continue: "▶️",
    'add-message': "💬",
    end: "🔚"
  }[action]

  if (error) {
    console.error(`${logPrefix} ${emoji} ${action.toUpperCase()} failed for ${shortId}:`, error)
  } else {
    const info = messageRole ? ` (${messageRole}${messageLength ? `, ${messageLength} chars` : ""})` : ""
    console.log(`${logPrefix} ${emoji} ${action.toUpperCase()} ${shortId}${info}`)
  }
}
